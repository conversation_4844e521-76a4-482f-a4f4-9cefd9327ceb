import 'package:flutter/material.dart';

/// 隐私政策页面
class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '隐私政策',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.orange.shade400,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题部分
              Row(
                children: [
                  Icon(
                    Icons.privacy_tip,
                    color: Colors.orange.shade400,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '隐私政策',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '最后更新时间：2024年1月1日',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 24),
              
              // 隐私政策内容
              _buildSection(
                '1. 信息收集',
                '我们会收集您主动提供的信息，如注册信息、宠物信息等。同时，我们也会收集设备使用过程中产生的数据，如健康监测数据、位置信息等。',
              ),
              _buildSection(
                '2. 信息使用',
                '我们使用收集的信息为您提供个性化服务，包括健康分析、行为监测、紧急提醒等功能。我们不会将您的个人信息用于其他商业目的。',
              ),
              _buildSection(
                '3. 信息共享',
                '我们不会向第三方出售、交易或转让您的个人信息。仅在法律要求或紧急情况下，我们可能会共享必要信息以保护您和宠物的安全。',
              ),
              _buildSection(
                '4. 数据安全',
                '我们采用行业标准的安全措施保护您的个人信息，包括数据加密、访问控制等技术手段，确保数据安全。',
              ),
              _buildSection(
                '5. 数据存储',
                '您的个人信息将存储在安全的服务器中。我们会根据法律要求和业务需要确定数据保存期限，过期数据将被安全删除。',
              ),
              _buildSection(
                '6. 用户权利',
                '您有权访问、更正、删除您的个人信息。如需行使这些权利，请通过应用内设置或联系客服。',
              ),
              _buildSection(
                '7. Cookie使用',
                '我们可能使用Cookie和类似技术来改善用户体验，您可以通过设备设置管理Cookie偏好。',
              ),
              _buildSection(
                '8. 政策更新',
                '我们可能会不时更新本隐私政策。重大变更将通过应用内通知或其他方式告知您。',
              ),
              
              const SizedBox(height: 32),
              
              // 同意按钮
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.shade400,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    '我已阅读并同意',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              height: 1.6,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
