import 'package:flutter/material.dart';

/// 用户服务协议页面
class UserAgreementScreen extends StatelessWidget {
  const UserAgreementScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '用户服务协议',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.orange.shade400,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题部分
              Row(
                children: [
                  Icon(
                    Icons.description,
                    color: Colors.orange.shade400,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    '用户服务协议',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '最后更新时间：2024年1月1日',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 24),

              // 协议内容
              _buildSection(
                '1. 服务条款',
                '欢迎使用宠物护理应用。本协议是您与我们之间关于使用本应用服务的法律协议。使用本应用即表示您同意遵守本协议的所有条款。',
              ),
              _buildSection(
                '2. 服务内容',
                '本应用为用户提供宠物健康监测、设备管理、防丢追踪等服务。我们致力于为您和您的宠物提供优质的护理体验。',
              ),
              _buildSection(
                '3. 用户责任',
                '用户应确保提供真实、准确的信息，合理使用应用功能，不得进行任何违法或损害他人利益的行为。',
              ),
              _buildSection(
                '4. 隐私保护',
                '我们重视您的隐私保护，将按照隐私政策的规定收集、使用和保护您的个人信息。',
              ),
              _buildSection(
                '5. 免责声明',
                '本应用提供的健康监测数据仅供参考，不能替代专业兽医的诊断和治疗建议。如有紧急情况，请及时就医。',
              ),
              _buildSection(
                '6. 协议修改',
                '我们保留随时修改本协议的权利。修改后的协议将在应用内公布，继续使用应用即视为同意修改后的协议。',
              ),
              _buildSection(
                '7. 联系我们',
                '如有任何问题或建议，请通过应用内客服功能联系我们，我们将竭诚为您服务。',
              ),

              const SizedBox(height: 32),

              // 同意按钮
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.shade400,
                    foregroundColor: Colors.white,
                    elevation: 2,
                  ),
                  child: const Text(
                    '我已阅读并同意',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 14,
              height: 1.6,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
